{"name": "mind-elixir-cloud", "version": "0.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mind-elixir/export-html": "^3.0.0", "@mind-elixir/export-xmind": "^2.0.1", "@mind-elixir/node-menu-neo": "^1.0.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "axios": "^1.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.0.8", "file-saver": "^2.0.5", "lucide-react": "^0.511.0", "mind-elixir": "5.0.0-beta.22", "next": "^14.0.0", "next-intl": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.0", "zustand": "^4.4.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.9", "@types/dompurify": "^3.0.5", "@types/file-saver": "^2.0.5", "@types/node": "^20.4.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.14", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.25", "tailwindcss": "^3.3.2", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.2"}}
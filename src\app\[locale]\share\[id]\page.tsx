'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import dynamic from 'next/dynamic'
import connect from '@/connect'
import { MindElixirData, Options } from 'mind-elixir'
import { MindMapItem } from '@/models/list'
// @ts-ignore
import nodeMenu from '@mind-elixir/node-menu-neo'

const MindElixirReact = dynamic(() => import('@/components/MindElixirReact'), {
  ssr: false,
  loading: () => (
    <div className="h-screen flex items-center justify-center">Loading...</div>
  ),
})

export default function MapSharePage() {
  const params = useParams()
  const router = useRouter()
  const [mapData, setMapData] = useState<MindElixirData | undefined>(undefined)

  const plugins = [nodeMenu]
  const options: Options = {
    el: '',
    direction: 2,
    draggable: false,
    editable: false,
    contextMenu: false,
    toolBar: true,
    keypress: false,
  }

  const mapId = params.id as string

  useEffect(() => {
    const fetchMap = async () => {
      try {
        const res = await connect.get<never, { data: MindMapItem }>(
          `/api/public/${mapId}`
        )
        setMapData(res.data.content)
      } catch (error) {
        console.error('Failed to fetch map:', error)
        router.push('/404')
      }
    }

    if (mapId) {
      fetchMap()
    }
  }, [mapId, router])

  return (
    <div className="h-screen">
      <MindElixirReact
        data={mapData}
        plugins={plugins}
        options={options}
        className="h-full"
      />
    </div>
  )
}
